import React, { useState, useEffect } from 'react';
import { Container, <PERSON>, Col, Card, Button, Form, Table, Alert, Spinner, Tabs, Tab, Badge, InputGroup } from 'react-bootstrap';
import {
  FaPlus,
  FaPencilAlt,
  FaTrashAlt,
  FaImage,
  FaArrowUp,
  FaArrowDown,
  FaArrowLeft,
  FaLink,
  FaEye,
  FaEyeSlash,
  FaDesktop,
  FaMobile,
  FaExclamationTriangle
} from 'react-icons/fa';
import { DragDropContext, Droppable, Draggable } from '@hello-pangea/dnd';
import {
  fetchCarousels,
  createCarousel,
  updateCarousel,
  deleteCarousel,
  fetchSlides,
  createSlide,
  updateSlide,
  deleteSlide,
  reorderSlides
} from '../../services/contentService';
import ImageManager from '../GestionCommerciale/ImageManager';
import ProfessionalModal from '../../ui-component/extended/ProfessionalModal';

const CarouselManagement = () => {
  // State for carousels
  const [carousels, setCarousels] = useState([]);
  const [carouselForm, setCarouselForm] = useState({
    name: '',
    description: '',
    is_active: true,
    ordre: 0
  });
  const [editingCarouselId, setEditingCarouselId] = useState(null);
  const [carouselLoading, setCarouselLoading] = useState(false);
  const [carouselSubmitting, setCarouselSubmitting] = useState(false);

  // State for slides
  const [selectedCarouselId, setSelectedCarouselId] = useState(null);
  const [slides, setSlides] = useState([]);
  const [slideForm, setSlideForm] = useState({
    title: '',
    content: '',
    button_text: '',
    button_link: '',
    is_active: true,
    ordre: 0
  });
  const [editingSlideId, setEditingSlideId] = useState(null);
  const [slideLoading, setSlideLoading] = useState(false);
  const [slideSubmitting, setSlideSubmitting] = useState(false);

  // UI state
  const [activeTab, setActiveTab] = useState('carousels');
  const [showCarouselModal, setShowCarouselModal] = useState(false);
  const [showSlideModal, setShowSlideModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [itemToDelete, setItemToDelete] = useState({ type: '', id: null });
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [selectedSlideId, setSelectedSlideId] = useState(null);
  const [imageType, setImageType] = useState('desktop');

  // Load carousels
  const loadCarousels = async () => {
    setCarouselLoading(true);
    setError('');
    try {
      const data = await fetchCarousels();
      setCarousels(data);
    } catch (e) {
      setError(`Error loading carousels: ${e.message}`);
    }
    setCarouselLoading(false);
  };

  // Load slides for a carousel
  const loadSlides = async (carouselId) => {
    if (!carouselId) return;

    setSlideLoading(true);
    setError('');
    try {
      const data = await fetchSlides(carouselId);
      setSlides(data);
      setSelectedCarouselId(carouselId);
      setActiveTab('slides');
    } catch (e) {
      setError(`Error loading slides: ${e.message}`);
    }
    setSlideLoading(false);
  };

  // Initial data load
  useEffect(() => {
    loadCarousels();
  }, []);

  // Handle carousel form changes
  const handleCarouselChange = (e) => {
    const { name, value, type, checked } = e.target;
    setCarouselForm({
      ...carouselForm,
      [name]: type === 'checkbox' ? checked : value
    });
  };

  // Handle slide form changes
  const handleSlideChange = (e) => {
    const { name, value, type, checked } = e.target;
    setSlideForm({
      ...slideForm,
      [name]: type === 'checkbox' ? checked : value
    });
  };

  // Submit carousel form
  const handleCarouselSubmit = async () => {
    if (!carouselForm.name) {
      setError('Carousel name is required');
      return;
    }

    setCarouselSubmitting(true);
    setError('');

    try {
      if (editingCarouselId) {
        await updateCarousel(editingCarouselId, carouselForm);
        setSuccess('Carousel updated successfully');
      } else {
        await createCarousel(carouselForm);
        setSuccess('Carousel created successfully');
      }

      setShowCarouselModal(false);
      resetCarouselForm();
      loadCarousels();
    } catch (e) {
      setError(`Error saving carousel: ${e.message}`);
    }

    setCarouselSubmitting(false);
    setTimeout(() => setSuccess(''), 3000);
  };

  // Submit slide form
  const handleSlideSubmit = async () => {
    if (!slideForm.title) {
      setError('Slide title is required');
      return;
    }

    setSlideSubmitting(true);
    setError('');

    try {
      if (editingSlideId) {
        await updateSlide(editingSlideId, slideForm);
        setSuccess('Slide updated successfully');
      } else {
        await createSlide(selectedCarouselId, slideForm);
        setSuccess('Slide created successfully');
      }

      setShowSlideModal(false);
      resetSlideForm();
      loadSlides(selectedCarouselId);
    } catch (e) {
      setError(`Error saving slide: ${e.message}`);
    }

    setSlideSubmitting(false);
    setTimeout(() => setSuccess(''), 3000);
  };

  // Reset carousel form
  const resetCarouselForm = () => {
    setCarouselForm({
      name: '',
      description: '',
      is_active: true,
      ordre: 0
    });
    setEditingCarouselId(null);
  };

  // Reset slide form
  const resetSlideForm = () => {
    setSlideForm({
      title: '',
      content: '',
      button_text: '',
      button_link: '',
      is_active: true,
      ordre: 0
    });
    setEditingSlideId(null);
  };

  // Edit carousel
  const handleEditCarousel = (carousel) => {
    setCarouselForm({
      name: carousel.name,
      description: carousel.description || '',
      is_active: carousel.is_active,
      ordre: carousel.ordre || 0
    });
    setEditingCarouselId(carousel.id);
    setShowCarouselModal(true);
  };

  // Edit slide
  const handleEditSlide = (slide) => {
    setSlideForm({
      title: slide.title,
      content: slide.content || '',
      button_text: slide.button_text || '',
      button_link: slide.button_link || '',
      is_active: slide.is_active,
      ordre: slide.ordre || 0
    });
    setEditingSlideId(slide.id);
    setShowSlideModal(true);
  };

  // Confirm delete
  const confirmDelete = (type, id) => {
    setItemToDelete({ type, id });
    setShowDeleteModal(true);
  };

  // Handle delete
  const handleDelete = async () => {
    setError('');

    try {
      const { type, id } = itemToDelete;

      if (type === 'carousel') {
        await deleteCarousel(id);
        loadCarousels();
        setSuccess('Carousel deleted successfully');
      } else if (type === 'slide') {
        await deleteSlide(id);
        loadSlides(selectedCarouselId);
        setSuccess('Slide deleted successfully');
      }
    } catch (e) {
      setError(`Error deleting item: ${e.message}`);
    }

    setShowDeleteModal(false);
    setTimeout(() => setSuccess(''), 3000);
  };

  // Handle slide reordering
  const handleDragEnd = async (result) => {
    if (!result.destination) return;

    const items = Array.from(slides);
    const [reorderedItem] = items.splice(result.source.index, 1);
    items.splice(result.destination.index, 0, reorderedItem);

    // Update local state immediately for better UX
    setSlides(items);

    try {
      await reorderSlides(selectedCarouselId, items);
      setSuccess('Slides reordered successfully');
    } catch (e) {
      setError(`Error reordering slides: ${e.message}`);
      // Revert to previous state on error
      loadSlides(selectedCarouselId);
    }

    setTimeout(() => setSuccess(''), 3000);
  };

  // Open image manager for a slide
  const openImageManager = (slideId) => {
    setSelectedSlideId(slideId);
    setActiveTab('images');
  };

  return (
    <Container fluid className="py-4">
      {/* Header */}
      <div className="mb-4">
        <h2 className="fw-bold text-primary mb-2">
          <FaImage className="me-2" />
          Carousel Management
        </h2>
        <p className="text-muted">Manage carousels and slides for your website.</p>
      </div>

      {error && <Alert variant="danger">{error}</Alert>}
      {success && <Alert variant="success">{success}</Alert>}

      {/* Tabs */}
      <Card className="shadow-sm mb-4 border-0">
        <Card.Body className="p-0">
          <Tabs activeKey={activeTab} onSelect={setActiveTab} className="mb-0 nav-tabs-custom" fill>
            <Tab
              eventKey="carousels"
              title={
                <span>
                  <FaImage className="me-2" />
                  Carousels
                </span>
              }
            />
            <Tab
              eventKey="slides"
              title={
                <span>
                  <FaDesktop className="me-2" />
                  Slides
                </span>
              }
              disabled={!selectedCarouselId}
            />
            <Tab
              eventKey="images"
              title={
                <span>
                  <FaImage className="me-2" />
                  Images
                </span>
              }
              disabled={!selectedSlideId}
            />
          </Tabs>
        </Card.Body>
      </Card>

      {/* Tab Content */}
      {activeTab === 'carousels' && (
        <>
          <div className="d-flex justify-content-end mb-3">
            <Button
              variant="primary"
              onClick={() => {
                resetCarouselForm();
                setShowCarouselModal(true);
              }}
            >
              <FaPlus className="me-2" />
              Add New Carousel
            </Button>
          </div>

          {carouselLoading ? (
            <div className="text-center py-5">
              <Spinner animation="border" variant="primary" />
              <p className="mt-3 text-muted">Loading carousels...</p>
            </div>
          ) : carousels.length === 0 ? (
            <div className="text-center py-5 border rounded">
              <div className="mb-3">
                <FaImage style={{ fontSize: '3rem' }} className="text-muted" />
              </div>
              <p className="text-muted">No carousels found.</p>
              <Button
                variant="primary"
                onClick={() => {
                  resetCarouselForm();
                  setShowCarouselModal(true);
                }}
              >
                <FaPlus className="me-2" />
                Create First Carousel
              </Button>
            </div>
          ) : (
            <Table hover responsive className="align-middle">
              <thead className="bg-light">
                <tr>
                  <th>ID</th>
                  <th>Nom</th>
                  <th>Description</th>
                  <th>Statut</th>
                  <th>Slides</th>
                  <th>Informations</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {carousels.map((carousel) => (
                  <tr key={carousel.id}>
                    <td>{carousel.id}</td>
                    <td>
                      <span className="fw-medium">{carousel.name}</span>
                    </td>
                    <td>
                      <div className="text-truncate" style={{ maxWidth: '200px' }}>
                        {carousel.description || <span className="text-muted fst-italic">No description</span>}
                      </div>
                    </td>
                    <td>{carousel.is_active ? <Badge bg="success">Active</Badge> : <Badge bg="secondary">Inactive</Badge>}</td>
                    <td>
                      <Badge bg="info">{carousel.slides_count || 0} slides</Badge>
                    </td>
                    <td>
                      <div className="d-flex flex-wrap gap-1">
                        <Badge bg="light" text="dark" className="border">
                          Ordre: {carousel.ordre}
                        </Badge>
                        <Badge bg="light" text="dark" className="border">
                          Créé le: {new Date(carousel.created_at).toLocaleDateString()}
                        </Badge>
                      </div>
                    </td>
                    <td>
                      <Button size="sm" variant="outline-primary" className="me-1" onClick={() => loadSlides(carousel.id)}>
                        <FaDesktop className="me-1" /> Slides
                      </Button>
                      <Button size="sm" variant="outline-secondary" className="me-1" onClick={() => handleEditCarousel(carousel)}>
                        <FaPencilAlt className="me-1" /> Edit
                      </Button>
                      <Button size="sm" variant="outline-danger" onClick={() => confirmDelete('carousel', carousel.id)}>
                        <FaTrashAlt className="me-1" /> Delete
                      </Button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </Table>
          )}
        </>
      )}

      {activeTab === 'images' && (
        <>
          <div className="d-flex justify-content-between align-items-center mb-4">
            <div>
              <h5 className="mb-1 fw-bold text-primary">
                <FaImage className="me-2" />
                Gestion des Images
              </h5>
              <p className="text-muted mb-0">
                Gérez les images pour la diapositive: <strong>{slides.find((s) => s.id === selectedSlideId)?.title}</strong>
              </p>
              <div className="mt-2">
                <Badge bg="info" className="me-2">
                  <FaDesktop className="me-1" />
                  Images Desktop & Mobile
                </Badge>
                <Badge bg="secondary">Formats: JPG, PNG, GIF, WebP</Badge>
              </div>
            </div>
            <div className="d-flex gap-2">
              <Button variant="outline-secondary" onClick={() => setActiveTab('slides')} className="d-flex align-items-center">
                <FaArrowLeft className="me-2" />
                Retour aux diapositives
              </Button>
            </div>
          </div>

          <ImageManager modelType="carousel_slide" modelId={selectedSlideId} />
        </>
      )}

      {activeTab === 'slides' && (
        <>
          <div className="d-flex justify-content-between align-items-center mb-3">
            <div>
              <h5 className="mb-0">Slides for Carousel: {carousels.find((c) => c.id === selectedCarouselId)?.name}</h5>
              <p className="text-muted mb-0">Drag and drop slides to reorder them</p>
            </div>
            <div className="d-flex gap-2">
              <Button variant="outline-secondary" onClick={() => setActiveTab('carousels')}>
                Back to Carousels
              </Button>
              <Button
                variant="primary"
                onClick={() => {
                  resetSlideForm();
                  setShowSlideModal(true);
                }}
              >
                <FaPlus className="me-2" />
                Add New Slide
              </Button>
            </div>
          </div>

          {slideLoading ? (
            <div className="text-center py-5">
              <Spinner animation="border" variant="primary" />
              <p className="mt-3 text-muted">Loading slides...</p>
            </div>
          ) : slides.length === 0 ? (
            <div className="text-center py-5 border rounded">
              <div className="mb-3">
                <FaDesktop style={{ fontSize: '3rem' }} className="text-muted" />
              </div>
              <p className="text-muted">No slides found for this carousel.</p>
              <Button
                variant="primary"
                onClick={() => {
                  resetSlideForm();
                  setShowSlideModal(true);
                }}
              >
                <FaPlus className="me-2" />
                Create First Slide
              </Button>
            </div>
          ) : (
            <DragDropContext onDragEnd={handleDragEnd}>
              <Droppable droppableId="slides">
                {(provided) => (
                  <div {...provided.droppableProps} ref={provided.innerRef}>
                    <Table hover responsive className="align-middle">
                      <thead className="bg-light">
                        <tr>
                          <th style={{ width: '60px' }}>Ordre</th>
                          <th style={{ width: '60px' }}>ID</th>
                          <th>Titre</th>
                          <th>Description</th>
                          <th>Bouton</th>
                          <th>Statut</th>
                          <th>Image</th>
                          <th>Actions</th>
                        </tr>
                      </thead>
                      <tbody>
                        {slides.map((slide, index) => (
                          <Draggable key={slide.id} draggableId={`slide-${slide.id}`} index={index}>
                            {(provided) => (
                              <tr ref={provided.innerRef} {...provided.draggableProps} {...provided.dragHandleProps}>
                                <td>
                                  <Badge bg="primary" pill>
                                    {index + 1}
                                  </Badge>
                                </td>
                                <td>{slide.id}</td>
                                <td>
                                  <div className="fw-medium">{slide.title}</div>
                                  {slide.subtitle && <div className="text-muted small">{slide.subtitle}</div>}
                                </td>
                                <td>
                                  <div className="text-truncate" style={{ maxWidth: '200px' }}>
                                    {slide.content || <span className="text-muted fst-italic">No content</span>}
                                  </div>
                                </td>
                                <td>
                                  {slide.button_text ? (
                                    <div>
                                      <Badge bg="info">{slide.button_text}</Badge>
                                      {slide.button_link && (
                                        <div className="text-muted small text-truncate" style={{ maxWidth: '150px' }}>
                                          <FaLink className="me-1" size="0.75em" />
                                          {slide.button_link}
                                        </div>
                                      )}
                                    </div>
                                  ) : (
                                    <span className="text-muted fst-italic">No button</span>
                                  )}
                                </td>
                                <td>
                                  {slide.is_active ? (
                                    <Badge bg="success">
                                      <FaEye className="me-1" /> Active
                                    </Badge>
                                  ) : (
                                    <Badge bg="secondary">
                                      <FaEyeSlash className="me-1" /> Inactive
                                    </Badge>
                                  )}
                                </td>
                                <td>
                                  <div className="d-flex gap-1">
                                    {slide.primary_image_url ? (
                                      <div className="position-relative">
                                        <img
                                          src={slide.primary_image_url}
                                          alt={slide.title}
                                          style={{ width: '50px', height: '30px', objectFit: 'cover' }}
                                          className="rounded"
                                        />
                                        <Button
                                          size="sm"
                                          variant="outline-primary"
                                          onClick={() => openImageManager(slide.id)}
                                          title="Gérer les images"
                                          className="ms-2"
                                        >
                                          <FaImage />
                                        </Button>
                                      </div>
                                    ) : (
                                      <Button
                                        size="sm"
                                        variant="outline-secondary"
                                        onClick={() => openImageManager(slide.id)}
                                        title="Ajouter une image"
                                      >
                                        <FaImage /> Ajouter
                                      </Button>
                                    )}
                                  </div>
                                </td>
                                <td>
                                  <Button size="sm" variant="outline-primary" className="me-1" onClick={() => handleEditSlide(slide)}>
                                    <FaPencilAlt />
                                  </Button>
                                  <Button size="sm" variant="outline-danger" onClick={() => confirmDelete('slide', slide.id)}>
                                    <FaTrashAlt />
                                  </Button>
                                </td>
                              </tr>
                            )}
                          </Draggable>
                        ))}
                        {provided.placeholder}
                      </tbody>
                    </Table>
                  </div>
                )}
              </Droppable>
            </DragDropContext>
          )}

          {/* Slide Modal */}
          <ProfessionalModal
            show={showSlideModal}
            onHide={() => setShowSlideModal(false)}
            title={editingSlideId ? 'Edit Slide' : 'Add New Slide'}
            subtitle="Configure slide content and display settings"
            icon={<FaDesktop />}
            size="lg"
            primaryAction={handleSlideSubmit}
            secondaryAction={() => setShowSlideModal(false)}
            primaryText="Save Slide"
            secondaryText="Cancel"
            loading={slideSubmitting}
            loadingText="Processing..."
            disabled={slideSubmitting}
            variant="primary"
          >
            <Form>
              <Row>
                <Col md={6}>
                  <Form.Group className="mb-3">
                    <Form.Label>Title</Form.Label>
                    <Form.Control
                      type="text"
                      name="title"
                      value={slideForm.title}
                      onChange={handleSlideChange}
                      placeholder="Enter slide title"
                      required
                    />
                  </Form.Group>
                </Col>
                <Col md={6}>
                  <Form.Group className="mb-3">
                    <Form.Label>Ordre</Form.Label>
                    <Form.Control
                      type="number"
                      name="ordre"
                      value={slideForm.ordre}
                      onChange={handleSlideChange}
                      placeholder="Ordre d'affichage"
                      min="0"
                    />
                  </Form.Group>
                </Col>
              </Row>
              <Form.Group className="mb-3">
                <Form.Label>Content</Form.Label>
                <Form.Control
                  as="textarea"
                  rows={3}
                  name="content"
                  value={slideForm.content}
                  onChange={handleSlideChange}
                  placeholder="Enter slide content"
                />
              </Form.Group>
              <Row>
                <Col md={6}>
                  <Form.Group className="mb-3">
                    <Form.Label>Button Text</Form.Label>
                    <Form.Control
                      type="text"
                      name="button_text"
                      value={slideForm.button_text}
                      onChange={handleSlideChange}
                      placeholder="Enter button text"
                    />
                  </Form.Group>
                </Col>
                <Col md={6}>
                  <Form.Group className="mb-3">
                    <Form.Label>Button Link</Form.Label>
                    <Form.Control
                      type="text"
                      name="button_link"
                      value={slideForm.button_link}
                      onChange={handleSlideChange}
                      placeholder="Enter button link"
                    />
                  </Form.Group>
                </Col>
              </Row>
              <Row>
                <Col md={6}>
                  <Form.Group className="mb-3">
                    <Form.Check
                      type="checkbox"
                      id="is_active"
                      name="is_active"
                      label="Active"
                      checked={slideForm.is_active}
                      onChange={handleSlideChange}
                    />
                  </Form.Group>
                </Col>
                <Col md={6}>
                  <Form.Group className="mb-3">
                    <Form.Check
                      type="checkbox"
                      id="open_in_new_tab"
                      name="open_in_new_tab"
                      label="Open link in new tab"
                      checked={slideForm.open_in_new_tab}
                      onChange={handleSlideChange}
                    />
                  </Form.Group>
                </Col>
              </Row>
              <Alert variant="info">
                <FaImage className="me-2" />
                After saving the slide, you can add desktop and mobile images from the slides list.
              </Alert>
            </Form>
          </ProfessionalModal>
        </>
      )}

      {/* Carousel Modal */}
      <ProfessionalModal
        show={showCarouselModal}
        onHide={() => setShowCarouselModal(false)}
        title={editingCarouselId ? 'Edit Carousel' : 'Add New Carousel'}
        subtitle="Configure carousel settings and display options"
        icon={<FaImage />}
        size="md"
        primaryAction={handleCarouselSubmit}
        secondaryAction={() => setShowCarouselModal(false)}
        primaryText="Save Carousel"
        secondaryText="Cancel"
        loading={carouselSubmitting}
        loadingText="Processing..."
        disabled={carouselSubmitting}
        variant="primary"
      >
        <Form>
          <Form.Group className="mb-3">
            <Form.Label>Name</Form.Label>
            <Form.Control
              type="text"
              name="name"
              value={carouselForm.name}
              onChange={handleCarouselChange}
              placeholder="Enter carousel name"
              required
            />
          </Form.Group>
          <Form.Group className="mb-3">
            <Form.Label>Description</Form.Label>
            <Form.Control
              as="textarea"
              rows={3}
              name="description"
              value={carouselForm.description}
              onChange={handleCarouselChange}
              placeholder="Enter carousel description"
            />
          </Form.Group>
          <Form.Group className="mb-3">
            <Form.Check
              type="checkbox"
              id="is_active"
              name="is_active"
              label="Active"
              checked={carouselForm.is_active}
              onChange={handleCarouselChange}
            />
          </Form.Group>
          <Form.Group className="mb-3">
            <Form.Label>Ordre</Form.Label>
            <Form.Control type="number" name="ordre" value={carouselForm.ordre} onChange={handleCarouselChange} min="0" />
            <Form.Text className="text-muted">Ordre d'affichage du carrousel</Form.Text>
          </Form.Group>
        </Form>
      </ProfessionalModal>

      {/* Delete Confirmation Modal */}
      <ProfessionalModal
        show={showDeleteModal}
        onHide={() => setShowDeleteModal(false)}
        title="Confirm Delete"
        subtitle="This action cannot be undone"
        icon={<FaExclamationTriangle />}
        size="sm"
        primaryAction={handleDelete}
        secondaryAction={() => setShowDeleteModal(false)}
        primaryText="Delete"
        secondaryText="Cancel"
        primaryVariant="danger"
        variant="danger"
      >
        <div className="text-center">
          {itemToDelete.type === 'carousel' && (
            <p className="mb-0">
              Are you sure you want to delete this carousel? This will also delete all slides associated with this carousel.
            </p>
          )}
          {itemToDelete.type === 'slide' && <p className="mb-0">Are you sure you want to delete this slide?</p>}
        </div>
      </ProfessionalModal>

      {/* Custom CSS for professional tabs styling */}
      <style jsx="true">{`
        .nav-tabs-custom .nav-link {
          color: #495057;
          font-weight: 500;
          padding: 1rem 1.5rem;
          border-radius: 0;
          border: none;
          border-bottom: 3px solid transparent;
          transition: all 0.3s ease;
        }
        .nav-tabs-custom .nav-link.active {
          color: #2196f3;
          background: transparent;
          border-bottom: 3px solid #2196f3;
        }
        .nav-tabs-custom .nav-link:hover:not(.active) {
          border-bottom: 3px solid #e9ecef;
          color: #2196f3;
        }
        .nav-tabs-custom .nav-link:disabled {
          color: #6c757d;
          opacity: 0.6;
          cursor: not-allowed;
        }
        .nav-tabs-custom .nav-link:disabled:hover {
          border-bottom: 3px solid transparent;
          color: #6c757d;
        }
      `}</style>
    </Container>
  );
};

export default CarouselManagement;
