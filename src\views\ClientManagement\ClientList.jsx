import { useState, useEffect } from 'react';

// material-ui
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  CircularProgress,
  Alert,
  IconButton,
  TextField,
  InputAdornment,
  Pagination,
  FormControl,
  Select,
  MenuItem,
  InputLabel
} from '@mui/material';
import { IconUserPlus, IconSearch } from '@tabler/icons-react';

// Bootstrap components
import { Badge } from 'react-bootstrap';

// project imports
import MainCard from 'ui-component/cards/MainCard';
import { fetchClients } from '../../services/clientService';

// assets

// ==============================|| CLIENT LIST ||============================== //

const ClientList = () => {
  const [isLoading, setLoading] = useState(true);
  const [clients, setClients] = useState([]);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filteredClients, setFilteredClients] = useState([]);

  // Pagination states
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  const handleSearchChange = (event) => {
    const term = event.target.value;
    setSearchTerm(term);
    setCurrentPage(1); // Reset to first page when searching

    if (term === '') {
      setFilteredClients(clients);
    } else {
      const filtered = clients.filter(
        (client) =>
          client.name?.toLowerCase().includes(term.toLowerCase()) ||
          client.email?.toLowerCase().includes(term.toLowerCase()) ||
          client.type_client?.toLowerCase().includes(term.toLowerCase()) ||
          client.profil_remise?.toLowerCase().includes(term.toLowerCase()) ||
          (client.roles && client.roles.some((role) => role.toLowerCase().includes(term.toLowerCase())))
      );
      setFilteredClients(filtered);
    }
  };

  // Pagination logic
  const totalPages = Math.ceil(filteredClients.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentClients = filteredClients.slice(startIndex, endIndex);

  // Handle pagination
  const handlePageChange = (event, pageNumber) => {
    setCurrentPage(pageNumber);
  };

  const handleItemsPerPageChange = (event) => {
    setItemsPerPage(event.target.value);
    setCurrentPage(1); // Reset to first page
  };

  const loadClients = async () => {
    try {
      setLoading(true);
      setError(null);
      console.log('🔄 Fetching clients from API...');
      const data = await fetchClients();
      console.log('✅ Clients data received:', data);

      // Handle different response formats
      const clientsArray = Array.isArray(data) ? data : data?.data || [];
      console.log('📊 Processed clients array:', clientsArray);

      setClients(clientsArray);
      setFilteredClients(clientsArray);
    } catch (err) {
      console.error('❌ Error loading clients:', err);
      setError('Erreur lors du chargement des clients: ' + err.message);
      setClients([]);
      setFilteredClients([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadClients();
  }, []);

  const renderClientTable = () => {
    if (isLoading) {
      return (
        <Box display="flex" justifyContent="center" p={3}>
          <CircularProgress />
        </Box>
      );
    }

    if (error) {
      return (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      );
    }

    if (!filteredClients || filteredClients.length === 0) {
      return (
        <Typography variant="body1" sx={{ p: 2, textAlign: 'center' }}>
          Aucun client trouvé.
        </Typography>
      );
    }

    return (
      <Box>
        <TableContainer component={Paper} sx={{ overflowX: 'auto' }}>
          <Table sx={{ minWidth: 1200 }}>
            <TableHead>
              <TableRow>
                <TableCell>ID</TableCell>
                <TableCell>Nom</TableCell>
                <TableCell>Email</TableCell>
                <TableCell>Rôles</TableCell>
                <TableCell>Type Client</TableCell>
                <TableCell>Remise Personnelle</TableCell>
                <TableCell>Remise Effective</TableCell>
                <TableCell>Profil Remise</TableCell>
                <TableCell>Groupe Client</TableCell>
                <TableCell>Date d'inscription</TableCell>
                <TableCell>Dernière mise à jour</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {currentClients.map((client) => (
                <TableRow key={client.id}>
                  <TableCell>
                    <Badge bg="secondary">{client.id || 'N/A'}</Badge>
                  </TableCell>
                  <TableCell>
                    <strong>{client.name || 'N/A'}</strong>
                  </TableCell>
                  <TableCell>{client.email || 'N/A'}</TableCell>
                  <TableCell>
                    {client.roles && client.roles.length > 0
                      ? client.roles.map((role, index) => (
                          <Badge key={index} bg="info" className="me-1">
                            {role}
                          </Badge>
                        ))
                      : 'N/A'}
                  </TableCell>
                  <TableCell>
                    <Badge bg="primary">{client.type_client || 'normal'}</Badge>
                  </TableCell>
                  <TableCell>{client.remise_personnelle ? `${client.remise_personnelle}%` : '0%'}</TableCell>
                  <TableCell>{client.remise_effective ? `${client.remise_effective}%` : '0%'}</TableCell>
                  <TableCell>
                    <Badge bg="success">{client.profil_remise || 'standard'}</Badge>
                  </TableCell>
                  <TableCell>{client.groupe_client_id ? <Badge bg="warning">Groupe #{client.groupe_client_id}</Badge> : 'Aucun'}</TableCell>
                  <TableCell>{client.created_at ? new Date(client.created_at).toLocaleDateString('fr-FR') : 'N/A'}</TableCell>
                  <TableCell>{client.updated_at ? new Date(client.updated_at).toLocaleDateString('fr-FR') : 'N/A'}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>

        {/* Pagination */}
        {totalPages > 1 && (
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mt: 2 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <Typography variant="body2" color="text.secondary">
                Affichage de {startIndex + 1} à {Math.min(endIndex, filteredClients.length)} sur {filteredClients.length} client(s)
              </Typography>
              <FormControl size="small" sx={{ minWidth: 120 }}>
                <InputLabel>Par page</InputLabel>
                <Select value={itemsPerPage} label="Par page" onChange={handleItemsPerPageChange}>
                  <MenuItem value={5}>5 par page</MenuItem>
                  <MenuItem value={10}>10 par page</MenuItem>
                  <MenuItem value={25}>25 par page</MenuItem>
                  <MenuItem value={50}>50 par page</MenuItem>
                </Select>
              </FormControl>
            </Box>
            <Pagination count={totalPages} page={currentPage} onChange={handlePageChange} color="primary" showFirstButton showLastButton />
          </Box>
        )}
      </Box>
    );
  };

  return (
    <MainCard
      title="Client Management"
      secondary={
        <Button variant="contained" startIcon={<IconUserPlus />}>
          Add Client
        </Button>
      }
    >
      <Box sx={{ width: '100%' }}>
        {/* Search Field */}
        <Box sx={{ mb: 2 }}>
          <TextField
            fullWidth
            variant="outlined"
            placeholder="Rechercher par nom, email, type de client, profil de remise..."
            value={searchTerm}
            onChange={handleSearchChange}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <IconSearch />
                </InputAdornment>
              )
            }}
          />
        </Box>

        <Typography variant="h5" gutterBottom sx={{ mt: 2 }}>
          Tous les Clients ({filteredClients.length})
        </Typography>
        {renderClientTable()}
      </Box>
    </MainCard>
  );
};

export default ClientList;
